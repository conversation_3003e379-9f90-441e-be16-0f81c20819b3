*** Using Compiler 'V5.06 update 7 (build 960)', folder: 'E:\keil5\ARM\ARMCompiler_506_Windows_x86_b960\Bin'
Build target 'Template'
compiling app.c...
app.c(54): warning:  #188-D: enumerated type mixed with another type
  static MotorTask_t current_task = {0};
app.c(118): error:  #268: declaration may not appear after executable statement in block
      uint32_t duration = calculate_run_time(angle, speed_rpm);
app.c(119): error:  #268: declaration may not appear after executable statement in block
      uint16_t pwm_freq = calculate_pwm_freq(speed_rpm);
app.c(144): warning:  #223-D: function "get_system_time_ms" declared implicitly
      current_task.start_time = get_system_time_ms();
app.c(245): warning:  #223-D: function "get_system_time_ms" declared implicitly
          uint32_t current_time = get_system_time_ms();
app.c(333): error:  #159: declaration is incompatible with previous "get_system_time_ms"  (declared at line 144)
  uint32_t get_system_time_ms(void) {
app.c: 3 warnings, 3 errors
"..\OBJ\Template.axf" - 3 Error(s), 3 Warning(s).
Target not created.
Build Time Elapsed:  00:00:03
