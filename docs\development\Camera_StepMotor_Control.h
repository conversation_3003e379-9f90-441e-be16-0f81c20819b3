/**
 * 2025年电赛E题专用步进电机控制系统头文件
 * 
 * 应用场景：摄像头识别 -> 串口角度指令 -> 电机精确转动
 * 
 * 主要功能：
 * 1. 非阻塞电机控制，响应摄像头指令
 * 2. 串口命令解析和执行
 * 3. 精确角度控制和位置跟踪
 * 4. 实时状态反馈
 * 
 * 串口指令格式：
 * - "M1:90.5"     -> 电机1转动90.5度
 * - "M2:-45.0"    -> 电机2反转45度  
 * - "MB:180.0"    -> 双电机同步转动180度
 * - "POS"         -> 查询当前位置
 * - "STOP"        -> 紧急停止
 * - "RESET"       -> 位置归零
 * 
 * 使用示例：
 * ```c
 * int main(void) {
 *     // 系统初始化
 *     delay_init(72);
 *     uart_init(115200);
 *     
 *     // 初始化电机控制系统
 *     Camera_StepMotor_Init();
 *     
 *     while(1) {
 *         // 处理电机任务和串口命令
 *         Camera_StepMotor_Process();
 *         
 *         // 其他业务逻辑...
 *         delay_ms(10);
 *     }
 * }
 * 
 * // 在串口中断中调用
 * void USART1_IRQHandler(void) {
 *     if(USART_GetITStatus(USART1, USART_IT_RXNE) != RESET) {
 *         uint8_t data = USART_ReceiveData(USART1);
 *         Camera_StepMotor_UART_Handler(data);
 *     }
 * }
 * ```
 * 
 * 版本：V1.0 电赛专用版
 */

#ifndef __CAMERA_STEPMOTOR_CONTROL_H
#define __CAMERA_STEPMOTOR_CONTROL_H

#include "sys.h"
#include "stm32f10x.h"

// ==================== 错误码定义 ====================
#define CAMERA_MOTOR_OK         0       // 成功
#define CAMERA_MOTOR_BUSY       1       // 系统忙
#define CAMERA_MOTOR_ERROR      2       // 错误

// ==================== 电机ID定义 ====================
#define MOTOR_1                 1       // 电机1
#define MOTOR_2                 2       // 电机2  
#define MOTOR_BOTH              3       // 双电机

// ==================== 核心API函数 ====================

/**
 * 系统初始化
 * 功能：初始化步进电机控制系统，替代原有的ATD5984_Init()
 * 参数：无
 * 返回：无
 * 说明：必须在使用其他功能前调用
 */
void Camera_StepMotor_Init(void);

/**
 * 主处理函数
 * 功能：处理电机任务和串口命令，非阻塞执行
 * 参数：无
 * 返回：无
 * 说明：必须在主循环中定期调用，建议10ms调用一次
 */
void Camera_StepMotor_Process(void);

/**
 * 串口数据处理
 * 功能：处理串口接收到的命令数据
 * 参数：data - 接收到的字节数据
 * 返回：无
 * 说明：在串口中断中调用，自动解析命令
 */
void Camera_StepMotor_UART_Handler(uint8_t data);

/**
 * 检查系统状态
 * 功能：检查电机是否空闲，可以接受新指令
 * 参数：无
 * 返回：1-空闲，0-忙碌
 * 说明：用于判断是否可以发送新的控制指令
 */
uint8_t Camera_StepMotor_IsIdle(void);

/**
 * 获取当前位置
 * 功能：获取两个电机的当前角度位置
 * 参数：motor1_pos - 电机1位置指针
 *       motor2_pos - 电机2位置指针
 * 返回：无
 * 说明：返回相对于初始位置的累计角度
 */
void Camera_StepMotor_GetPosition(float* motor1_pos, float* motor2_pos);

/**
 * 直接角度控制
 * 功能：程序内部直接控制电机转动（非串口命令）
 * 参数：motor_id - 电机ID (1, 2, 3)
 *       angle - 转动角度（正数正转，负数反转）
 *       speed_rpm - 转速 (5-100 RPM)
 * 返回：0-成功，1-系统忙
 * 说明：用于程序内部直接控制，不依赖串口命令
 */
uint8_t Camera_StepMotor_RotateAngle(uint8_t motor_id, float angle, uint16_t speed_rpm);

// ==================== 辅助函数 ====================

/**
 * 系统时间获取
 * 功能：获取系统运行时间（毫秒）
 * 参数：无
 * 返回：系统时间（毫秒）
 * 说明：用户需要根据实际系统实现此函数
 */
uint32_t get_system_time_ms(void);

// ==================== 便捷宏定义 ====================

// 常用角度控制宏
#define CAMERA_ROTATE_M1(angle)     Camera_StepMotor_RotateAngle(MOTOR_1, angle, 30)
#define CAMERA_ROTATE_M2(angle)     Camera_StepMotor_RotateAngle(MOTOR_2, angle, 30)
#define CAMERA_ROTATE_BOTH(angle)   Camera_StepMotor_RotateAngle(MOTOR_BOTH, angle, 30)

// 状态检查宏
#define CAMERA_MOTOR_READY()        Camera_StepMotor_IsIdle()

// ==================== 串口命令说明 ====================

/*
支持的串口命令格式：

1. 单电机控制：
   - "M1:90.5"      -> 电机1正转90.5度
   - "M1:-45.0"     -> 电机1反转45度
   - "M2:180.0"     -> 电机2正转180度
   - "M2:-90.0"     -> 电机2反转90度

2. 双电机同步控制：
   - "MB:360.0"     -> 双电机同步正转360度
   - "MB:-180.0"    -> 双电机同步反转180度

3. 状态查询：
   - "POS"          -> 返回当前位置信息
   
4. 系统控制：
   - "STOP"         -> 立即停止所有电机
   - "RESET"        -> 位置计数器归零

命令格式要求：
- 以换行符(\n)或回车符(\r)结束
- 角度支持小数点，如90.5度
- 负数表示反转方向
- 命令不区分大小写

返回信息格式：
- 执行确认："电机1开始转动90.5度"
- 位置信息："当前位置: M1=90.5°, M2=-45.0°"
- 完成通知："转动完成！当前位置: M1=135.5°, M2=0.0°"
- 错误信息："电机忙，指令被忽略"
*/

// ==================== 集成指南 ====================

/*
在您的main.c中的集成方法：

1. 替换原有初始化：
   // 删除这些行：
   // ATD5984_Init();
   // STEP12_PWM_Init(7199, 6);
   
   // 替换为：
   Camera_StepMotor_Init();

2. 在主循环中添加：
   while(1) {
       Camera_StepMotor_Process();  // 必须调用
       
       // 您的其他业务逻辑...
       delay_ms(10);
   }

3. 在串口中断中添加：
   void USART1_IRQHandler(void) {
       if(USART_GetITStatus(USART1, USART_IT_RXNE) != RESET) {
           uint8_t data = USART_ReceiveData(USART1);
           Camera_StepMotor_UART_Handler(data);  // 添加这行
       }
   }

4. 摄像头模块使用示例：
   摄像头识别到目标后，通过串口发送：
   - "M1:45.0"   -> 水平转动45度
   - "M2:-30.0"  -> 垂直转动-30度
   - "POS"       -> 查询当前位置
*/

#endif /* __CAMERA_STEPMOTOR_CONTROL_H */
