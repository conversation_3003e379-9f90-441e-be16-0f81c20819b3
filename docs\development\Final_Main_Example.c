/**
 * 2025年电赛E题最终主程序示例
 * 
 * 特点：
 * 1. 避免SysTick_Handler冲突
 * 2. 使用简单可靠的时间管理
 * 3. 完全非阻塞的电机控制
 * 4. 专为摄像头控制优化
 * 
 * 时间管理方案：
 * - 不重写SysTick_Handler
 * - 基于主循环的时间累计
 * - 每个循环周期10ms，精度足够
 */

#include "sys.h"
#include "delay.h"
#include "usart.h"
#include "Camera_StepMotor_Control.h"
#include <stdio.h>

// ==================== 外部函数声明 ====================
// 这些函数在Camera_StepMotor_Control.c中实现
extern void update_system_time(uint32_t interval_ms);
extern void reset_system_time(void);

// ==================== 串口中断处理 ====================

/**
 * 串口1中断服务函数
 * 处理摄像头发送的角度指令
 */
void USART1_IRQHandler(void) {
    if(USART_GetITStatus(USART1, USART_IT_RXNE) != RESET) {
        uint8_t data = USART_ReceiveData(USART1);
        
        // 将接收到的数据传递给电机控制系统
        Camera_StepMotor_UART_Handler(data);
        
        USART_ClearITPendingBit(USART1, USART_IT_RXNE);
    }
}

// ==================== 主程序 ====================

/**
 * 主函数 - 最终版本
 * 解决了SysTick冲突问题，使用简单可靠的时间管理
 */
int main(void) {
    // ==================== 系统初始化 ====================
    
    // 中断优先级分组
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
    
    // 延时函数初始化（使用现有的SysTick配置）
    delay_init(72);
    
    // 串口初始化（与摄像头通信）
    uart_init(115200);
    
    printf("\r\n");
    printf("========================================\r\n");
    printf("  2025年电赛E题步进电机控制系统\r\n");
    printf("  版本：V1.1 最终版\r\n");
    printf("  时间管理：基于主循环（避免SysTick冲突）\r\n");
    printf("========================================\r\n");
    
    // ==================== 电机系统初始化 ====================
    
    // 初始化摄像头步进电机控制系统
    Camera_StepMotor_Init();
    
    // 重置系统时间
    reset_system_time();
    
    printf("系统初始化完成，等待摄像头指令...\r\n");
    printf("支持的串口命令：\r\n");
    printf("  M1:angle  - 电机1转动指定角度\r\n");
    printf("  M2:angle  - 电机2转动指定角度\r\n");
    printf("  MB:angle  - 双电机同步转动\r\n");
    printf("  POS       - 查询当前位置\r\n");
    printf("  STOP      - 紧急停止\r\n");
    printf("  RESET     - 位置归零\r\n");
    printf("========================================\r\n");
    
    // ==================== 主循环 ====================
    
    uint32_t loop_counter = 0;
    uint32_t last_status_time = 0;
    const uint32_t LOOP_INTERVAL_MS = 10;      // 主循环间隔10ms
    const uint32_t STATUS_INTERVAL_MS = 5000;  // 状态报告间隔5秒
    
    while(1) {
        // ==================== 时间管理 ====================
        
        // 更新系统时间（每个循环10ms）
        update_system_time(LOOP_INTERVAL_MS);
        loop_counter++;
        
        // ==================== 核心处理 ====================
        
        // 处理电机任务和串口命令（必须调用）
        Camera_StepMotor_Process();
        
        // ==================== 状态监控 ====================
        
        // 定期状态报告
        uint32_t current_time = get_system_time_ms();
        if (current_time - last_status_time >= STATUS_INTERVAL_MS) {
            float pos1, pos2;
            Camera_StepMotor_GetPosition(&pos1, &pos2);
            
            printf("系统状态: %s, 位置: M1=%.1f°, M2=%.1f°, 运行时间: %ld秒\r\n", 
                   Camera_StepMotor_IsIdle() ? "空闲" : "运行中",
                   pos1, pos2, current_time / 1000);
            
            last_status_time = current_time;
        }
        
        // ==================== 其他业务逻辑 ====================
        
        // 在这里可以添加其他处理：
        // - 摄像头数据处理
        // - LED状态指示
        // - 其他传感器读取
        // - 数据记录等
        
        // 示例：每1000个循环（10秒）输出一次调试信息
        if (loop_counter % 1000 == 0) {
            printf("主循环运行正常，循环计数: %ld\r\n", loop_counter);
        }
        
        // ==================== 循环控制 ====================
        
        // 固定循环周期，确保时间精度
        delay_ms(LOOP_INTERVAL_MS);
    }
}

// ==================== 测试和调试函数 ====================

/**
 * 系统自检函数
 * 可以在启动时调用，验证系统功能
 */
void system_self_test(void) {
    printf("开始系统自检...\r\n");
    
    // 测试时间系统
    uint32_t start_time = get_system_time_ms();
    delay_ms(1000);
    update_system_time(1000);  // 手动更新1秒
    uint32_t elapsed = get_system_time_ms() - start_time;
    printf("时间系统测试: 预期1000ms, 实际%ldms\r\n", elapsed);
    
    // 测试电机1
    printf("测试电机1转动90度...\r\n");
    Camera_StepMotor_RotateAngle(1, 90.0, 30);
    
    // 等待完成
    while(!Camera_StepMotor_IsIdle()) {
        Camera_StepMotor_Process();
        update_system_time(10);
        delay_ms(10);
    }
    
    // 测试电机2
    printf("测试电机2转动-90度...\r\n");
    Camera_StepMotor_RotateAngle(2, -90.0, 30);
    
    while(!Camera_StepMotor_IsIdle()) {
        Camera_StepMotor_Process();
        update_system_time(10);
        delay_ms(10);
    }
    
    // 回到原点
    printf("回到原点...\r\n");
    Camera_StepMotor_RotateAngle(1, -90.0, 30);
    while(!Camera_StepMotor_IsIdle()) {
        Camera_StepMotor_Process();
        update_system_time(10);
        delay_ms(10);
    }
    
    Camera_StepMotor_RotateAngle(2, 90.0, 30);
    while(!Camera_StepMotor_IsIdle()) {
        Camera_StepMotor_Process();
        update_system_time(10);
        delay_ms(10);
    }
    
    printf("系统自检完成\r\n");
}

/**
 * 演示摄像头控制序列
 */
void demo_camera_sequence(void) {
    printf("演示摄像头控制序列...\r\n");
    
    // 模拟摄像头发送的指令
    char* commands[] = {
        "M1:45.0",      // 水平右转
        "M2:30.0",      // 垂直上转
        "POS",          // 查询位置
        "MB:-22.5",     // 双电机调整
        "RESET"         // 归零
    };
    
    int cmd_count = sizeof(commands) / sizeof(commands[0]);
    
    for(int i = 0; i < cmd_count; i++) {
        printf("模拟指令: %s\r\n", commands[i]);
        
        // 模拟串口接收
        char* cmd = commands[i];
        while(*cmd) {
            Camera_StepMotor_UART_Handler(*cmd);
            cmd++;
        }
        Camera_StepMotor_UART_Handler('\n');
        
        // 等待执行完成
        while(!Camera_StepMotor_IsIdle()) {
            Camera_StepMotor_Process();
            update_system_time(10);
            delay_ms(10);
        }
        
        delay_ms(500);  // 指令间隔
        update_system_time(500);
    }
    
    printf("演示完成\r\n");
}

// ==================== 配置和优化 ====================

/**
 * 系统配置结构
 */
typedef struct {
    uint32_t loop_interval_ms;      // 主循环间隔
    uint32_t status_interval_ms;    // 状态报告间隔
    uint8_t debug_mode;             // 调试模式
    uint8_t auto_status;            // 自动状态报告
} SystemConfig_t;

static SystemConfig_t config = {
    .loop_interval_ms = 10,         // 10ms循环
    .status_interval_ms = 5000,     // 5秒状态报告
    .debug_mode = 1,                // 开启调试
    .auto_status = 1                // 开启自动状态
};

/**
 * 获取系统配置
 */
SystemConfig_t* get_system_config(void) {
    return &config;
}

/**
 * 性能监控
 */
void performance_monitor(void) {
    static uint32_t last_monitor_time = 0;
    static uint32_t loop_count = 0;
    
    loop_count++;
    
    uint32_t current_time = get_system_time_ms();
    if (current_time - last_monitor_time >= 10000) {  // 10秒统计
        uint32_t loops_per_second = loop_count / 10;
        printf("性能监控: %ld循环/秒 (目标100循环/秒)\r\n", loops_per_second);
        
        if (loops_per_second < 90) {
            printf("警告: 循环频率偏低，可能影响控制精度\r\n");
        }
        
        loop_count = 0;
        last_monitor_time = current_time;
    }
}
