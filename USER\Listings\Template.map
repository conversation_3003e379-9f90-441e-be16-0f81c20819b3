Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(i.main) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.main) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    main.o(i.main) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    main.o(i.main) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    main.o(i.main) refers to delay.o(i.delay_init) for delay_init
    main.o(i.main) refers to key.o(i.Key_Init) for Key_Init
    main.o(i.main) refers to adc.o(i.ADC1_Init) for ADC1_Init
    main.o(i.main) refers to usart.o(i.uart_init) for uart_init
    main.o(i.main) refers to atd5984.o(i.ATD5984_Init) for ATD5984_Init
    main.o(i.main) refers to atd5984.o(i.STEP12_PWM_Init) for STEP12_PWM_Init
    main.o(i.main) refers to tim.o(i.TIM2_Init) for TIM2_Init
    main.o(i.main) refers to adc.o(i.Get_adc_Average) for Get_adc_Average
    main.o(i.main) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    main.o(i.main) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    main.o(i.main) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    main.o(i.main) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    main.o(i.main) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(i.main) refers to noretval__2printf.o(.text) for __2printf
    main.o(i.main) refers to main.o(.data) for adc_val
    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    delay.o(i.delay_init) refers to delay.o(.data) for fac_us
    delay.o(i.delay_ms) refers to delay.o(.data) for fac_ms
    delay.o(i.delay_us) refers to delay.o(.data) for fac_us
    usart.o(i._sys_exit) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.fputc) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart_init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.uart_init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.uart_init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart.o(i.uart_init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.uart_init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.usart1_send) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    startup_stm32f10x_hd.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_hd.o(RESET) refers to tim.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f10x_hd.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(HEAP) for Heap_Mem
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(STACK) for Stack_Mem
    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_bkp.o(i.BKP_DeInit) refers to stm32f10x_rcc.o(i.RCC_BackupResetCmd) for RCC_BackupResetCmd
    stm32f10x_can.o(i.CAN_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_can.o(i.CAN_GetITStatus) refers to stm32f10x_can.o(i.CheckITStatus) for CheckITStatus
    stm32f10x_cec.o(i.CEC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_dac.o(i.DAC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_flash.o(i.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) for FLASH_WaitForLastBank1Operation
    stm32f10x_flash.o(i.FLASH_EraseAllPages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus) for FLASH_GetReadOutProtectionStatus
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ErasePage) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_pwr.o(i.PWR_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_spi.o(i.I2S_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI3_Config) for TI3_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI4_Config) for TI4_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_wwdg.o(i.WWDG_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    atd5984.o(i.ATD5984_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    atd5984.o(i.ATD5984_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    atd5984.o(i.ATD5984_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    atd5984.o(i.ATD5984_Init) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    atd5984.o(i.STEP12_PWM_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    atd5984.o(i.STEP12_PWM_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    atd5984.o(i.STEP12_PWM_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    atd5984.o(i.STEP12_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC3Init) for TIM_OC3Init
    atd5984.o(i.STEP12_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC3PreloadConfig) for TIM_OC3PreloadConfig
    atd5984.o(i.STEP12_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC4Init) for TIM_OC4Init
    atd5984.o(i.STEP12_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC4PreloadConfig) for TIM_OC4PreloadConfig
    atd5984.o(i.STEP12_PWM_Init) refers to stm32f10x_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    atd5984.o(i.STEP12_PWM_Init) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    atd5984.o(i.STEP12_PWM_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    adc.o(i.ADC1_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    adc.o(i.ADC1_Init) refers to stm32f10x_rcc.o(i.RCC_ADCCLKConfig) for RCC_ADCCLKConfig
    adc.o(i.ADC1_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    adc.o(i.ADC1_Init) refers to stm32f10x_adc.o(i.ADC_Init) for ADC_Init
    adc.o(i.ADC1_Init) refers to stm32f10x_adc.o(i.ADC_Cmd) for ADC_Cmd
    adc.o(i.ADC1_Init) refers to stm32f10x_adc.o(i.ADC_ResetCalibration) for ADC_ResetCalibration
    adc.o(i.ADC1_Init) refers to stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus) for ADC_GetResetCalibrationStatus
    adc.o(i.ADC1_Init) refers to stm32f10x_adc.o(i.ADC_StartCalibration) for ADC_StartCalibration
    adc.o(i.ADC1_Init) refers to stm32f10x_adc.o(i.ADC_GetCalibrationStatus) for ADC_GetCalibrationStatus
    adc.o(i.Get_Adc1) refers to stm32f10x_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    adc.o(i.Get_Adc1) refers to stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd) for ADC_SoftwareStartConvCmd
    adc.o(i.Get_Adc1) refers to stm32f10x_adc.o(i.ADC_GetFlagStatus) for ADC_GetFlagStatus
    adc.o(i.Get_Adc1) refers to stm32f10x_adc.o(i.ADC_GetConversionValue) for ADC_GetConversionValue
    adc.o(i.Get_adc_Average) refers to adc.o(i.Get_Adc1) for Get_Adc1
    adc.o(i.Get_adc_Average) refers to delay.o(i.delay_ms) for delay_ms
    key.o(i.Key_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    key.o(i.Key_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    tim.o(i.TIM2_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    tim.o(i.TIM2_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    tim.o(i.TIM2_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    tim.o(i.TIM2_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    tim.o(i.TIM2_Init) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    tim.o(i.TIM2_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    tim.o(i.TIM2_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart.o(.data) for __stdout
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart.o(.data) for __stdout
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(i.fputc) for fputc
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_char_common.o(.text) refers to __printf.o(.text) for __printf
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_hd.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(i._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing system_stm32f10x.o(.data), (20 bytes).
    Removing delay.o(i.delay_us), (60 bytes).
    Removing sys.o(.emb_text), (6 bytes).
    Removing sys.o(i.INTX_DISABLE), (4 bytes).
    Removing sys.o(i.INTX_ENABLE), (4 bytes).
    Removing sys.o(i.JTAG_Set), (48 bytes).
    Removing sys.o(i.MY_NVIC_SetVectorTable), (20 bytes).
    Removing sys.o(i.WFI_SET), (4 bytes).
    Removing usart.o(i.usart1_send), (28 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (40 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_DMACmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DeInit), (92 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (36 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (28 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (130 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (36 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearFlag), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_DeInit), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetFlagStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetITStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ITConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_RTCOutputConfig), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ReadBackupRegister), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_SetRTCCalibrationValue), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinCmd), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinLevelConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_WriteBackupRegister), (28 bytes).
    Removing stm32f10x_can.o(i.CAN_CancelTransmit), (48 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearFlag), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearITPendingBit), (168 bytes).
    Removing stm32f10x_can.o(i.CAN_DBGFreeze), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_DeInit), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_FIFORelease), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_FilterInit), (264 bytes).
    Removing stm32f10x_can.o(i.CAN_GetFlagStatus), (120 bytes).
    Removing stm32f10x_can.o(i.CAN_GetITStatus), (288 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLSBTransmitErrorCounter), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLastErrorCode), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetReceiveErrorCounter), (10 bytes).
    Removing stm32f10x_can.o(i.CAN_ITConfig), (18 bytes).
    Removing stm32f10x_can.o(i.CAN_Init), (276 bytes).
    Removing stm32f10x_can.o(i.CAN_MessagePending), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_OperatingModeRequest), (162 bytes).
    Removing stm32f10x_can.o(i.CAN_Receive), (240 bytes).
    Removing stm32f10x_can.o(i.CAN_SlaveStartBank), (52 bytes).
    Removing stm32f10x_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f10x_can.o(i.CAN_TTComModeCmd), (118 bytes).
    Removing stm32f10x_can.o(i.CAN_Transmit), (294 bytes).
    Removing stm32f10x_can.o(i.CAN_TransmitStatus), (160 bytes).
    Removing stm32f10x_can.o(i.CAN_WakeUp), (48 bytes).
    Removing stm32f10x_can.o(i.CheckITStatus), (18 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearFlag), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearITPendingBit), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_Cmd), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_DeInit), (22 bytes).
    Removing stm32f10x_cec.o(i.CEC_EndOfMessageCmd), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetFlagStatus), (48 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetITStatus), (40 bytes).
    Removing stm32f10x_cec.o(i.CEC_ITConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_Init), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_OwnAddressConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_ReceiveDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SendDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SetPrescaler), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_StartOfMessage), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcBlockCRC), (36 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcCRC), (16 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_Cmd), (40 bytes).
    Removing stm32f10x_dac.o(i.DAC_DMACmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_DeInit), (22 bytes).
    Removing stm32f10x_dac.o(i.DAC_DualSoftwareTriggerCmd), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_GetDataOutputValue), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_Init), (52 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel1Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel2Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetDualChannelData), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_SoftwareTriggerCmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_WaveGenerationCmd), (40 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_Config), (32 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetREVID), (12 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearFlag), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearITPendingBit), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_Cmd), (24 bytes).
    Removing stm32f10x_dma.o(i.DMA_DeInit), (332 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetCurrDataCounter), (8 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetFlagStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetITStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_ITConfig), (18 bytes).
    Removing stm32f10x_dma.o(i.DMA_Init), (60 bytes).
    Removing stm32f10x_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f10x_dma.o(i.DMA_StructInit), (26 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (24 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetITStatus), (40 bytes).
    Removing stm32f10x_exti.o(i.EXTI_Init), (148 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EnableWriteProtection), (196 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllPages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseOptionBytes), (168 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ErasePage), (76 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetBank1Status), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetFlagStatus), (48 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetPrefetchBufferStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetStatus), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetUserOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_HalfCycleAccessCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ITConfig), (32 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Lock), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_LockBank1), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramHalfWord), (64 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramOptionByteData), (84 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramWord), (108 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ReadOutProtection), (172 bytes).
    Removing stm32f10x_flash.o(i.FLASH_SetLatency), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Unlock), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UnlockBank1), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UserOptionByteConfig), (104 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation), (38 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastOperation), (38 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearFlag), (64 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearITPendingBit), (72 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetECC), (28 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetFlagStatus), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetITStatus), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ITConfig), (128 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDDeInit), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDECCCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDInit), (136 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd), (52 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMDeInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMInit), (230 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMStructInit), (114 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDCmd), (48 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDDeInit), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDInit), (132 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (64 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinRemapConfig), (144 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ARPCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_AcknowledgeConfig), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CalculatePEC), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CheckEvent), (42 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearFlag), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Cmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMACmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMALastTransferCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DeInit), (56 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DualAddressCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_FastModeDutyCycleConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GeneralCallCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTART), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTOP), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetFlagStatus), (58 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetITStatus), (38 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetLastEvent), (26 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetPEC), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ITConfig), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Init), (236 bytes).
    Removing stm32f10x_i2c.o(i.I2C_NACKPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_OwnAddress2Config), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_PECPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReadRegister), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReceiveData), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SMBusAlertConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Send7bitAddress), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SendData), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SoftwareResetCmd), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StretchClockCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StructInit), (30 bytes).
    Removing stm32f10x_i2c.o(i.I2C_TransmitPEC), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_GetFlagStatus), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_ClearFlag), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_DeInit), (22 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTANDBYMode), (52 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTOPMode), (64 bytes).
    Removing stm32f10x_pwr.o(i.PWR_GetFlagStatus), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDLevelConfig), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearFlag), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_EnterConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ExitConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetCounter), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetDivider), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetFlagStatus), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetITStatus), (36 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ITConfig), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetAlarm), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetCounter), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetPrescaler), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForLastTask), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForSynchro), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CEATAITCmd), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearFlag), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClockCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CommandCompletionCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DMACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataConfig), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataStructInit), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DeInit), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetCommandResponse), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFlagStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetITStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetPowerState), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetResponse), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ITConfig), (32 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_Init), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ReadData), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCEATACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCommand), (44 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendSDIOSuspendCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetPowerState), (28 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOOperation), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOReadWaitMode), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StartSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StopSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StructInit), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_WriteData), (12 bytes).
    Removing stm32f10x_spi.o(i.I2S_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.I2S_Init), (232 bytes).
    Removing stm32f10x_spi.o(i.I2S_StructInit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_BiDirectionalLineConfig), (28 bytes).
    Removing stm32f10x_spi.o(i.SPI_CalculateCRC), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_DataSizeConfig), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRC), (16 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRCPolynomial), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DMACmd), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DeInit), (88 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetFlagStatus), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetITStatus), (52 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ITConfig), (32 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ReceiveData), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_SendData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_Init), (60 bytes).
    Removing stm32f10x_spi.o(i.SPI_NSSInternalSoftwareConfig), (30 bytes).
    Removing stm32f10x_spi.o(i.SPI_SSOutputCmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (128 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TI3_Config), (144 bytes).
    Removing stm32f10x_tim.o(i.TI4_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (488 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (54 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (28 bytes).
    Removing stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig), (66 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCounter), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (172 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_InternalClockConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1Init), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2Init), (164 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (82 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (62 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearITPendingBit), (30 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_GetFlagStatus), (26 bytes).
    Removing stm32f10x_usart.o(i.USART_GetITStatus), (84 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiveData), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_DeInit), (22 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_Enable), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_EnableIT), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_GetFlagStatus), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetCounter), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetPrescaler), (24 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetWindowValue), (40 bytes).
    Removing key.o(i.Key_Scan), (20 bytes).

451 unused section(s) (total 17532 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ..\CORE\core_cm3.c                       0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CORE\startup_stm32f10x_hd.s           0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    ..\HAREWARE\ADC\adc.c                    0x00000000   Number         0  adc.o ABSOLUTE
    ..\HAREWARE\ATD5984\ATD5984.c            0x00000000   Number         0  atd5984.o ABSOLUTE
    ..\HAREWARE\KEY\KEY.c                    0x00000000   Number         0  key.o ABSOLUTE
    ..\HAREWARE\TIM\TIM.c                    0x00000000   Number         0  tim.o ABSOLUTE
    ..\STM32F10x_FWLib\src\misc.c            0x00000000   Number         0  misc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_adc.c   0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_bkp.c   0x00000000   Number         0  stm32f10x_bkp.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_can.c   0x00000000   Number         0  stm32f10x_can.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_cec.c   0x00000000   Number         0  stm32f10x_cec.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_crc.c   0x00000000   Number         0  stm32f10x_crc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_dac.c   0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_dbgmcu.c 0x00000000   Number         0  stm32f10x_dbgmcu.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_dma.c   0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_exti.c  0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_flash.c 0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_fsmc.c  0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_gpio.c  0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_i2c.c   0x00000000   Number         0  stm32f10x_i2c.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_iwdg.c  0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_pwr.c   0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_rcc.c   0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_rtc.c   0x00000000   Number         0  stm32f10x_rtc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_sdio.c  0x00000000   Number         0  stm32f10x_sdio.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_spi.c   0x00000000   Number         0  stm32f10x_spi.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_tim.c   0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_usart.c 0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_wwdg.c  0x00000000   Number         0  stm32f10x_wwdg.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\CORE\\core_cm3.c                     0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f10x_it.c                           0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    system_stm32f10x.c                       0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    !!!main                                  0x08000130   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000138   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x0800016c   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000188   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001a4   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x080001a4   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000017  0x080001aa   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080001ae   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x080001b0   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x080001b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x080001b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080001b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x080001b6   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x080001c0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080001c0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080001c0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080001c0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080001c0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080001c0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080001c0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080001c0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080001c0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080001c0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080001c0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080001c0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080001c0   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080001c2   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080001c4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080001c4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x080001c4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080001c4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x080001c4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x080001c4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x080001c4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x080001c4   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x080001c6   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080001c6   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080001c6   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080001cc   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080001cc   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080001d0   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080001d0   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080001d8   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080001da   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080001da   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080001de   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080001e4   Section       64  startup_stm32f10x_hd.o(.text)
    .text                                    0x08000224   Section        2  use_no_semi_2.o(.text)
    .text                                    0x08000228   Section        0  noretval__2printf.o(.text)
    .text                                    0x08000240   Section        0  __printf.o(.text)
    .text                                    0x080002a8   Section        0  heapauxi.o(.text)
    .text                                    0x080002ae   Section        2  use_no_semi.o(.text)
    .text                                    0x080002b0   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x080002b3   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x080006d0   Section        0  _printf_char_file.o(.text)
    .text                                    0x080006f4   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x080006fc   Section      138  lludiv10.o(.text)
    .text                                    0x08000788   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000789   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x080007b8   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08000838   Section        0  bigflt0.o(.text)
    .text                                    0x0800091c   Section        0  ferror.o(.text)
    .text                                    0x08000924   Section        8  libspace.o(.text)
    .text                                    0x0800092c   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000976   Section        0  exit.o(.text)
    .text                                    0x08000988   Section      128  strcmpv7m.o(.text)
    CL$$btod_d2e                             0x08000a08   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08000a46   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08000a8c   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08000aec   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08000e24   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08000f00   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08000f2a   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x08000f54   Section      580  btod.o(CL$$btod_mult_common)
    i.ADC1_Init                              0x08001198   Section        0  adc.o(i.ADC1_Init)
    i.ADC_Cmd                                0x0800122c   Section        0  stm32f10x_adc.o(i.ADC_Cmd)
    i.ADC_GetCalibrationStatus               0x08001242   Section        0  stm32f10x_adc.o(i.ADC_GetCalibrationStatus)
    i.ADC_GetConversionValue                 0x08001256   Section        0  stm32f10x_adc.o(i.ADC_GetConversionValue)
    i.ADC_GetFlagStatus                      0x0800125e   Section        0  stm32f10x_adc.o(i.ADC_GetFlagStatus)
    i.ADC_GetResetCalibrationStatus          0x08001270   Section        0  stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus)
    i.ADC_Init                               0x08001284   Section        0  stm32f10x_adc.o(i.ADC_Init)
    i.ADC_RegularChannelConfig               0x080012d4   Section        0  stm32f10x_adc.o(i.ADC_RegularChannelConfig)
    i.ADC_ResetCalibration                   0x0800138c   Section        0  stm32f10x_adc.o(i.ADC_ResetCalibration)
    i.ADC_SoftwareStartConvCmd               0x08001396   Section        0  stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd)
    i.ADC_StartCalibration                   0x080013ac   Section        0  stm32f10x_adc.o(i.ADC_StartCalibration)
    i.ATD5984_Init                           0x080013b8   Section        0  atd5984.o(i.ATD5984_Init)
    i.BusFault_Handler                       0x08001430   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x08001434   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.GPIO_Init                              0x08001436   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_ResetBits                         0x0800154c   Section        0  stm32f10x_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x08001550   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.Get_Adc1                               0x08001554   Section        0  adc.o(i.Get_Adc1)
    i.Get_adc_Average                        0x08001588   Section        0  adc.o(i.Get_adc_Average)
    i.HardFault_Handler                      0x080015b6   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.Key_Init                               0x080015bc   Section        0  key.o(i.Key_Init)
    i.MemManage_Handler                      0x080015e8   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x080015ec   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x080015f0   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_PriorityGroupConfig               0x08001660   Section        0  misc.o(i.NVIC_PriorityGroupConfig)
    i.PendSV_Handler                         0x08001674   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.RCC_ADCCLKConfig                       0x08001678   Section        0  stm32f10x_rcc.o(i.RCC_ADCCLKConfig)
    i.RCC_APB1PeriphClockCmd                 0x08001690   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x080016b0   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x080016d0   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.STEP12_PWM_Init                        0x080017a4   Section        0  atd5984.o(i.STEP12_PWM_Init)
    i.SVC_Handler                            0x0800184c   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.SetSysClock                            0x0800184e   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x0800184f   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x08001858   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x08001859   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.SysTick_Handler                        0x08001938   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SystemInit                             0x0800193c   Section        0  system_stm32f10x.o(i.SystemInit)
    i.TIM2_IRQHandler                        0x0800199c   Section        0  tim.o(i.TIM2_IRQHandler)
    i.TIM2_Init                              0x080019b4   Section        0  tim.o(i.TIM2_Init)
    i.TIM_ARRPreloadConfig                   0x08001a0c   Section        0  stm32f10x_tim.o(i.TIM_ARRPreloadConfig)
    i.TIM_ClearITPendingBit                  0x08001a24   Section        0  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    i.TIM_Cmd                                0x08001a2a   Section        0  stm32f10x_tim.o(i.TIM_Cmd)
    i.TIM_CtrlPWMOutputs                     0x08001a42   Section        0  stm32f10x_tim.o(i.TIM_CtrlPWMOutputs)
    i.TIM_GetITStatus                        0x08001a60   Section        0  stm32f10x_tim.o(i.TIM_GetITStatus)
    i.TIM_ITConfig                           0x08001a82   Section        0  stm32f10x_tim.o(i.TIM_ITConfig)
    i.TIM_OC3Init                            0x08001a94   Section        0  stm32f10x_tim.o(i.TIM_OC3Init)
    i.TIM_OC3PreloadConfig                   0x08001b34   Section        0  stm32f10x_tim.o(i.TIM_OC3PreloadConfig)
    i.TIM_OC4Init                            0x08001b48   Section        0  stm32f10x_tim.o(i.TIM_OC4Init)
    i.TIM_OC4PreloadConfig                   0x08001bc4   Section        0  stm32f10x_tim.o(i.TIM_OC4PreloadConfig)
    i.TIM_TimeBaseInit                       0x08001be0   Section        0  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    i.USART_Cmd                              0x08001c84   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_ITConfig                         0x08001c9c   Section        0  stm32f10x_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x08001ce8   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.UsageFault_Handler                     0x08001dc0   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x08001dc4   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i._sys_exit                              0x08001dec   Section        0  usart.o(i._sys_exit)
    i.delay_init                             0x08001df4   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x08001e34   Section        0  delay.o(i.delay_ms)
    i.fputc                                  0x08001e70   Section        0  usart.o(i.fputc)
    i.main                                   0x08001e8c   Section        0  main.o(i.main)
    i.uart_init                              0x08001f44   Section        0  usart.o(i.uart_init)
    locale$$code                             0x08001fc8   Section       44  lc_numeric_c.o(locale$$code)
    x$fpl$d2f                                0x08001ff4   Section       98  d2f.o(x$fpl$d2f)
    x$fpl$ddiv                               0x08002058   Section      688  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x0800205f   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dfltu                              0x08002308   Section       38  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dmul                               0x08002330   Section      340  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x08002484   Section      156  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08002520   Section       12  dretinf.o(x$fpl$dretinf)
    x$fpl$f2d                                0x0800252c   Section       86  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x08002582   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x0800260e   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$printf1                            0x08002618   Section        4  printf1.o(x$fpl$printf1)
    .constdata                               0x0800261c   Section      148  bigflt0.o(.constdata)
    x$fpl$usenofp                            0x0800261c   Section        0  usenofp.o(x$fpl$usenofp)
    tenpwrs_x                                0x0800261c   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x08002658   Data          64  bigflt0.o(.constdata)
    locale$$data                             0x080026d0   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x080026d4   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x080026dc   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x080026e8   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x080026ea   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x080026eb   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x080026ec   Data           0  lc_numeric_c.o(locale$$data)
    .data                                    0x20000000   Section        8  main.o(.data)
    .data                                    0x20000008   Section        4  delay.o(.data)
    fac_us                                   0x20000008   Data           1  delay.o(.data)
    fac_ms                                   0x2000000a   Data           2  delay.o(.data)
    .data                                    0x2000000c   Section        4  usart.o(.data)
    .data                                    0x20000010   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x20000010   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000020   Data           4  stm32f10x_rcc.o(.data)
    .bss                                     0x20000024   Section       96  libspace.o(.bss)
    HEAP                                     0x20000088   Section      512  startup_stm32f10x_hd.o(HEAP)
    Heap_Mem                                 0x20000088   Data         512  startup_stm32f10x_hd.o(HEAP)
    STACK                                    0x20000288   Section     1024  startup_stm32f10x_hd.o(STACK)
    Stack_Mem                                0x20000288   Data        1024  startup_stm32f10x_hd.o(STACK)
    __initial_sp                             0x20000688   Data           0  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000139   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000147   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x0800016d   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000189   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_f                                0x080001a5   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_percent                          0x080001a5   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_percent_end                      0x080001ab   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080001af   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_heap_1                     0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x080001b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x080001b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080001b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x080001b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x080001c1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080001c1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080001c1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080001c1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080001c1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080001c1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x080001c1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080001c1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x080001c1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080001c1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x080001c1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080001c1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080001c1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x080001c3   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080001c5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x080001c5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080001c5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x080001c5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x080001c5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x080001c5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x080001c5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x080001c5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x080001c7   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080001c7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080001c7   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080001cd   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080001cd   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080001d1   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080001d1   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080001d9   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080001db   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080001db   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080001df   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080001e5   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART1_IRQHandler                        0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART2_IRQHandler                        0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __user_initial_stackheap                 0x08000201   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __use_no_semihosting                     0x08000225   Thumb Code     2  use_no_semi_2.o(.text)
    __2printf                                0x08000229   Thumb Code    20  noretval__2printf.o(.text)
    __printf                                 0x08000241   Thumb Code   104  __printf.o(.text)
    __use_two_region_memory                  0x080002a9   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080002ab   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080002ad   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x080002af   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080002af   Thumb Code     2  use_no_semi.o(.text)
    __lib_sel_fp_printf                      0x080002b1   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08000463   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_file                        0x080006d1   Thumb Code    32  _printf_char_file.o(.text)
    __rt_locale                              0x080006f5   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _ll_udiv10                               0x080006fd   Thumb Code   138  lludiv10.o(.text)
    _printf_char_common                      0x08000793   Thumb Code    32  _printf_char_common.o(.text)
    _printf_fp_infnan                        0x080007b9   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x08000839   Thumb Code   224  bigflt0.o(.text)
    ferror                                   0x0800091d   Thumb Code     8  ferror.o(.text)
    __user_libspace                          0x08000925   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000925   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000925   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x0800092d   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000977   Thumb Code    18  exit.o(.text)
    strcmp                                   0x08000989   Thumb Code   128  strcmpv7m.o(.text)
    _btod_d2e                                0x08000a09   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08000a47   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08000a8d   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08000aed   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08000e25   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08000f01   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08000f2b   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x08000f55   Thumb Code   580  btod.o(CL$$btod_mult_common)
    ADC1_Init                                0x08001199   Thumb Code   138  adc.o(i.ADC1_Init)
    ADC_Cmd                                  0x0800122d   Thumb Code    22  stm32f10x_adc.o(i.ADC_Cmd)
    ADC_GetCalibrationStatus                 0x08001243   Thumb Code    20  stm32f10x_adc.o(i.ADC_GetCalibrationStatus)
    ADC_GetConversionValue                   0x08001257   Thumb Code     8  stm32f10x_adc.o(i.ADC_GetConversionValue)
    ADC_GetFlagStatus                        0x0800125f   Thumb Code    18  stm32f10x_adc.o(i.ADC_GetFlagStatus)
    ADC_GetResetCalibrationStatus            0x08001271   Thumb Code    20  stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus)
    ADC_Init                                 0x08001285   Thumb Code    70  stm32f10x_adc.o(i.ADC_Init)
    ADC_RegularChannelConfig                 0x080012d5   Thumb Code   184  stm32f10x_adc.o(i.ADC_RegularChannelConfig)
    ADC_ResetCalibration                     0x0800138d   Thumb Code    10  stm32f10x_adc.o(i.ADC_ResetCalibration)
    ADC_SoftwareStartConvCmd                 0x08001397   Thumb Code    22  stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd)
    ADC_StartCalibration                     0x080013ad   Thumb Code    10  stm32f10x_adc.o(i.ADC_StartCalibration)
    ATD5984_Init                             0x080013b9   Thumb Code   112  atd5984.o(i.ATD5984_Init)
    BusFault_Handler                         0x08001431   Thumb Code     4  stm32f10x_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x08001435   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    GPIO_Init                                0x08001437   Thumb Code   278  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_ResetBits                           0x0800154d   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x08001551   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    Get_Adc1                                 0x08001555   Thumb Code    46  adc.o(i.Get_Adc1)
    Get_adc_Average                          0x08001589   Thumb Code    46  adc.o(i.Get_adc_Average)
    HardFault_Handler                        0x080015b7   Thumb Code     4  stm32f10x_it.o(i.HardFault_Handler)
    Key_Init                                 0x080015bd   Thumb Code    40  key.o(i.Key_Init)
    MemManage_Handler                        0x080015e9   Thumb Code     4  stm32f10x_it.o(i.MemManage_Handler)
    NMI_Handler                              0x080015ed   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    NVIC_Init                                0x080015f1   Thumb Code   100  misc.o(i.NVIC_Init)
    NVIC_PriorityGroupConfig                 0x08001661   Thumb Code    10  misc.o(i.NVIC_PriorityGroupConfig)
    PendSV_Handler                           0x08001675   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    RCC_ADCCLKConfig                         0x08001679   Thumb Code    18  stm32f10x_rcc.o(i.RCC_ADCCLKConfig)
    RCC_APB1PeriphClockCmd                   0x08001691   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x080016b1   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x080016d1   Thumb Code   192  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    STEP12_PWM_Init                          0x080017a5   Thumb Code   160  atd5984.o(i.STEP12_PWM_Init)
    SVC_Handler                              0x0800184d   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08001939   Thumb Code     2  stm32f10x_it.o(i.SysTick_Handler)
    SystemInit                               0x0800193d   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    TIM2_IRQHandler                          0x0800199d   Thumb Code    24  tim.o(i.TIM2_IRQHandler)
    TIM2_Init                                0x080019b5   Thumb Code    88  tim.o(i.TIM2_Init)
    TIM_ARRPreloadConfig                     0x08001a0d   Thumb Code    24  stm32f10x_tim.o(i.TIM_ARRPreloadConfig)
    TIM_ClearITPendingBit                    0x08001a25   Thumb Code     6  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    TIM_Cmd                                  0x08001a2b   Thumb Code    24  stm32f10x_tim.o(i.TIM_Cmd)
    TIM_CtrlPWMOutputs                       0x08001a43   Thumb Code    30  stm32f10x_tim.o(i.TIM_CtrlPWMOutputs)
    TIM_GetITStatus                          0x08001a61   Thumb Code    34  stm32f10x_tim.o(i.TIM_GetITStatus)
    TIM_ITConfig                             0x08001a83   Thumb Code    18  stm32f10x_tim.o(i.TIM_ITConfig)
    TIM_OC3Init                              0x08001a95   Thumb Code   150  stm32f10x_tim.o(i.TIM_OC3Init)
    TIM_OC3PreloadConfig                     0x08001b35   Thumb Code    18  stm32f10x_tim.o(i.TIM_OC3PreloadConfig)
    TIM_OC4Init                              0x08001b49   Thumb Code   114  stm32f10x_tim.o(i.TIM_OC4Init)
    TIM_OC4PreloadConfig                     0x08001bc5   Thumb Code    26  stm32f10x_tim.o(i.TIM_OC4PreloadConfig)
    TIM_TimeBaseInit                         0x08001be1   Thumb Code   122  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    USART_Cmd                                0x08001c85   Thumb Code    24  stm32f10x_usart.o(i.USART_Cmd)
    USART_ITConfig                           0x08001c9d   Thumb Code    74  stm32f10x_usart.o(i.USART_ITConfig)
    USART_Init                               0x08001ce9   Thumb Code   210  stm32f10x_usart.o(i.USART_Init)
    UsageFault_Handler                       0x08001dc1   Thumb Code     4  stm32f10x_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x08001dc5   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    _sys_exit                                0x08001ded   Thumb Code     6  usart.o(i._sys_exit)
    delay_init                               0x08001df5   Thumb Code    56  delay.o(i.delay_init)
    delay_ms                                 0x08001e35   Thumb Code    56  delay.o(i.delay_ms)
    fputc                                    0x08001e71   Thumb Code    24  usart.o(i.fputc)
    main                                     0x08001e8d   Thumb Code   140  main.o(i.main)
    uart_init                                0x08001f45   Thumb Code   124  usart.o(i.uart_init)
    _get_lc_numeric                          0x08001fc9   Thumb Code    44  lc_numeric_c.o(locale$$code)
    __aeabi_d2f                              0x08001ff5   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x08001ff5   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_ddiv                             0x08002059   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x08002059   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_ui2d                             0x08002309   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x08002309   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_dmul                             0x08002331   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08002331   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x08002485   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08002521   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_f2d                              0x0800252d   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x0800252d   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x08002583   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x0800260f   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    _printf_fp_dec                           0x08002619   Thumb Code     4  printf1.o(x$fpl$printf1)
    __I$use$fp                               0x0800261c   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x080026b0   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080026d0   Number         0  anon$$obj.o(Region$$Table)
    adc_val                                  0x20000000   Data           2  main.o(.data)
    voltage                                  0x20000004   Data           4  main.o(.data)
    __stdout                                 0x2000000c   Data           4  usart.o(.data)
    __libspace_start                         0x20000024   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000084   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001e5

  Load Region LR_1 (Base: 0x08000000, Size: 0x00002710, Max: 0xffffffff, ABSOLUTE)

    Execution Region ER_RO (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000026ec, Max: 0xffffffff, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO          384    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000008   Code   RO         3468  * !!!main             c_w.l(__main.o)
    0x08000138   0x08000138   0x00000034   Code   RO         3708    !!!scatter          c_w.l(__scatter.o)
    0x0800016c   0x0800016c   0x0000001a   Code   RO         3710    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000186   0x08000186   0x00000002   PAD
    0x08000188   0x08000188   0x0000001c   Code   RO         3712    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001a4   0x080001a4   0x00000000   Code   RO         3465    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001a4   0x080001a4   0x00000006   Code   RO         3464    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x080001aa   0x080001aa   0x00000004   Code   RO         3495    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080001ae   0x080001ae   0x00000002   Code   RO         3583    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO         3585    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO         3587    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO         3590    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO         3592    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO         3594    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000006   Code   RO         3595    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x080001b6   0x080001b6   0x00000000   Code   RO         3597    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080001b6   0x080001b6   0x00000000   Code   RO         3599    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080001b6   0x080001b6   0x00000000   Code   RO         3601    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080001b6   0x080001b6   0x0000000a   Code   RO         3602    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         3603    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         3605    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         3607    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         3609    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         3611    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         3613    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         3615    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         3617    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         3621    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         3623    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         3625    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         3627    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080001c0   0x080001c0   0x00000002   Code   RO         3628    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080001c2   0x080001c2   0x00000002   Code   RO         3656    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080001c4   0x080001c4   0x00000000   Code   RO         3665    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080001c4   0x080001c4   0x00000000   Code   RO         3667    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080001c4   0x080001c4   0x00000000   Code   RO         3669    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080001c4   0x080001c4   0x00000000   Code   RO         3672    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080001c4   0x080001c4   0x00000000   Code   RO         3675    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080001c4   0x080001c4   0x00000000   Code   RO         3677    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080001c4   0x080001c4   0x00000000   Code   RO         3680    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x080001c4   0x080001c4   0x00000002   Code   RO         3681    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x080001c6   0x080001c6   0x00000000   Code   RO         3490    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080001c6   0x080001c6   0x00000000   Code   RO         3506    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080001c6   0x080001c6   0x00000006   Code   RO         3518    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080001cc   0x080001cc   0x00000000   Code   RO         3508    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080001cc   0x080001cc   0x00000004   Code   RO         3509    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080001d0   0x080001d0   0x00000000   Code   RO         3511    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080001d0   0x080001d0   0x00000008   Code   RO         3512    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080001d8   0x080001d8   0x00000002   Code   RO         3629    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080001da   0x080001da   0x00000000   Code   RO         3636    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080001da   0x080001da   0x00000004   Code   RO         3637    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080001de   0x080001de   0x00000006   Code   RO         3638    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080001e4   0x080001e4   0x00000040   Code   RO          385  * .text               startup_stm32f10x_hd.o
    0x08000224   0x08000224   0x00000002   Code   RO         3436    .text               c_w.l(use_no_semi_2.o)
    0x08000226   0x08000226   0x00000002   PAD
    0x08000228   0x08000228   0x00000018   Code   RO         3440    .text               c_w.l(noretval__2printf.o)
    0x08000240   0x08000240   0x00000068   Code   RO         3442    .text               c_w.l(__printf.o)
    0x080002a8   0x080002a8   0x00000006   Code   RO         3466    .text               c_w.l(heapauxi.o)
    0x080002ae   0x080002ae   0x00000002   Code   RO         3488    .text               c_w.l(use_no_semi.o)
    0x080002b0   0x080002b0   0x0000041e   Code   RO         3491    .text               c_w.l(_printf_fp_dec.o)
    0x080006ce   0x080006ce   0x00000002   PAD
    0x080006d0   0x080006d0   0x00000024   Code   RO         3493    .text               c_w.l(_printf_char_file.o)
    0x080006f4   0x080006f4   0x00000008   Code   RO         3523    .text               c_w.l(rt_locale_intlibspace.o)
    0x080006fc   0x080006fc   0x0000008a   Code   RO         3525    .text               c_w.l(lludiv10.o)
    0x08000786   0x08000786   0x00000002   PAD
    0x08000788   0x08000788   0x00000030   Code   RO         3527    .text               c_w.l(_printf_char_common.o)
    0x080007b8   0x080007b8   0x00000080   Code   RO         3529    .text               c_w.l(_printf_fp_infnan.o)
    0x08000838   0x08000838   0x000000e4   Code   RO         3531    .text               c_w.l(bigflt0.o)
    0x0800091c   0x0800091c   0x00000008   Code   RO         3556    .text               c_w.l(ferror.o)
    0x08000924   0x08000924   0x00000008   Code   RO         3567    .text               c_w.l(libspace.o)
    0x0800092c   0x0800092c   0x0000004a   Code   RO         3570    .text               c_w.l(sys_stackheap_outer.o)
    0x08000976   0x08000976   0x00000012   Code   RO         3574    .text               c_w.l(exit.o)
    0x08000988   0x08000988   0x00000080   Code   RO         3576    .text               c_w.l(strcmpv7m.o)
    0x08000a08   0x08000a08   0x0000003e   Code   RO         3534    CL$$btod_d2e        c_w.l(btod.o)
    0x08000a46   0x08000a46   0x00000046   Code   RO         3536    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08000a8c   0x08000a8c   0x00000060   Code   RO         3535    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08000aec   0x08000aec   0x00000338   Code   RO         3544    CL$$btod_div_common  c_w.l(btod.o)
    0x08000e24   0x08000e24   0x000000dc   Code   RO         3541    CL$$btod_e2e        c_w.l(btod.o)
    0x08000f00   0x08000f00   0x0000002a   Code   RO         3538    CL$$btod_ediv       c_w.l(btod.o)
    0x08000f2a   0x08000f2a   0x0000002a   Code   RO         3537    CL$$btod_emul       c_w.l(btod.o)
    0x08000f54   0x08000f54   0x00000244   Code   RO         3543    CL$$btod_mult_common  c_w.l(btod.o)
    0x08001198   0x08001198   0x00000094   Code   RO         3374    i.ADC1_Init         adc.o
    0x0800122c   0x0800122c   0x00000016   Code   RO          431    i.ADC_Cmd           stm32f10x_adc.o
    0x08001242   0x08001242   0x00000014   Code   RO          439    i.ADC_GetCalibrationStatus  stm32f10x_adc.o
    0x08001256   0x08001256   0x00000008   Code   RO          440    i.ADC_GetConversionValue  stm32f10x_adc.o
    0x0800125e   0x0800125e   0x00000012   Code   RO          442    i.ADC_GetFlagStatus  stm32f10x_adc.o
    0x08001270   0x08001270   0x00000014   Code   RO          445    i.ADC_GetResetCalibrationStatus  stm32f10x_adc.o
    0x08001284   0x08001284   0x00000050   Code   RO          449    i.ADC_Init          stm32f10x_adc.o
    0x080012d4   0x080012d4   0x000000b8   Code   RO          453    i.ADC_RegularChannelConfig  stm32f10x_adc.o
    0x0800138c   0x0800138c   0x0000000a   Code   RO          454    i.ADC_ResetCalibration  stm32f10x_adc.o
    0x08001396   0x08001396   0x00000016   Code   RO          456    i.ADC_SoftwareStartConvCmd  stm32f10x_adc.o
    0x080013ac   0x080013ac   0x0000000a   Code   RO          458    i.ADC_StartCalibration  stm32f10x_adc.o
    0x080013b6   0x080013b6   0x00000002   PAD
    0x080013b8   0x080013b8   0x00000078   Code   RO         3356    i.ATD5984_Init      atd5984.o
    0x08001430   0x08001430   0x00000004   Code   RO          139    i.BusFault_Handler  stm32f10x_it.o
    0x08001434   0x08001434   0x00000002   Code   RO          140    i.DebugMon_Handler  stm32f10x_it.o
    0x08001436   0x08001436   0x00000116   Code   RO         1540    i.GPIO_Init         stm32f10x_gpio.o
    0x0800154c   0x0800154c   0x00000004   Code   RO         1547    i.GPIO_ResetBits    stm32f10x_gpio.o
    0x08001550   0x08001550   0x00000004   Code   RO         1548    i.GPIO_SetBits      stm32f10x_gpio.o
    0x08001554   0x08001554   0x00000034   Code   RO         3375    i.Get_Adc1          adc.o
    0x08001588   0x08001588   0x0000002e   Code   RO         3376    i.Get_adc_Average   adc.o
    0x080015b6   0x080015b6   0x00000004   Code   RO          141    i.HardFault_Handler  stm32f10x_it.o
    0x080015ba   0x080015ba   0x00000002   PAD
    0x080015bc   0x080015bc   0x0000002c   Code   RO         3398    i.Key_Init          key.o
    0x080015e8   0x080015e8   0x00000004   Code   RO          142    i.MemManage_Handler  stm32f10x_it.o
    0x080015ec   0x080015ec   0x00000002   Code   RO          143    i.NMI_Handler       stm32f10x_it.o
    0x080015ee   0x080015ee   0x00000002   PAD
    0x080015f0   0x080015f0   0x00000070   Code   RO          389    i.NVIC_Init         misc.o
    0x08001660   0x08001660   0x00000014   Code   RO          390    i.NVIC_PriorityGroupConfig  misc.o
    0x08001674   0x08001674   0x00000002   Code   RO          144    i.PendSV_Handler    stm32f10x_it.o
    0x08001676   0x08001676   0x00000002   PAD
    0x08001678   0x08001678   0x00000018   Code   RO         1954    i.RCC_ADCCLKConfig  stm32f10x_rcc.o
    0x08001690   0x08001690   0x00000020   Code   RO         1956    i.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x080016b0   0x080016b0   0x00000020   Code   RO         1958    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x080016d0   0x080016d0   0x000000d4   Code   RO         1966    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x080017a4   0x080017a4   0x000000a8   Code   RO         3357    i.STEP12_PWM_Init   atd5984.o
    0x0800184c   0x0800184c   0x00000002   Code   RO          145    i.SVC_Handler       stm32f10x_it.o
    0x0800184e   0x0800184e   0x00000008   Code   RO          228    i.SetSysClock       system_stm32f10x.o
    0x08001856   0x08001856   0x00000002   PAD
    0x08001858   0x08001858   0x000000e0   Code   RO          229    i.SetSysClockTo72   system_stm32f10x.o
    0x08001938   0x08001938   0x00000002   Code   RO          146    i.SysTick_Handler   stm32f10x_it.o
    0x0800193a   0x0800193a   0x00000002   PAD
    0x0800193c   0x0800193c   0x00000060   Code   RO          231    i.SystemInit        system_stm32f10x.o
    0x0800199c   0x0800199c   0x00000018   Code   RO         3416    i.TIM2_IRQHandler   tim.o
    0x080019b4   0x080019b4   0x00000058   Code   RO         3417    i.TIM2_Init         tim.o
    0x08001a0c   0x08001a0c   0x00000018   Code   RO         2578    i.TIM_ARRPreloadConfig  stm32f10x_tim.o
    0x08001a24   0x08001a24   0x00000006   Code   RO         2585    i.TIM_ClearITPendingBit  stm32f10x_tim.o
    0x08001a2a   0x08001a2a   0x00000018   Code   RO         2590    i.TIM_Cmd           stm32f10x_tim.o
    0x08001a42   0x08001a42   0x0000001e   Code   RO         2592    i.TIM_CtrlPWMOutputs  stm32f10x_tim.o
    0x08001a60   0x08001a60   0x00000022   Code   RO         2611    i.TIM_GetITStatus   stm32f10x_tim.o
    0x08001a82   0x08001a82   0x00000012   Code   RO         2615    i.TIM_ITConfig      stm32f10x_tim.o
    0x08001a94   0x08001a94   0x000000a0   Code   RO         2629    i.TIM_OC3Init       stm32f10x_tim.o
    0x08001b34   0x08001b34   0x00000012   Code   RO         2632    i.TIM_OC3PreloadConfig  stm32f10x_tim.o
    0x08001b46   0x08001b46   0x00000002   PAD
    0x08001b48   0x08001b48   0x0000007c   Code   RO         2634    i.TIM_OC4Init       stm32f10x_tim.o
    0x08001bc4   0x08001bc4   0x0000001a   Code   RO         2636    i.TIM_OC4PreloadConfig  stm32f10x_tim.o
    0x08001bde   0x08001bde   0x00000002   PAD
    0x08001be0   0x08001be0   0x000000a4   Code   RO         2661    i.TIM_TimeBaseInit  stm32f10x_tim.o
    0x08001c84   0x08001c84   0x00000018   Code   RO         3126    i.USART_Cmd         stm32f10x_usart.o
    0x08001c9c   0x08001c9c   0x0000004a   Code   RO         3132    i.USART_ITConfig    stm32f10x_usart.o
    0x08001ce6   0x08001ce6   0x00000002   PAD
    0x08001ce8   0x08001ce8   0x000000d8   Code   RO         3133    i.USART_Init        stm32f10x_usart.o
    0x08001dc0   0x08001dc0   0x00000004   Code   RO          147    i.UsageFault_Handler  stm32f10x_it.o
    0x08001dc4   0x08001dc4   0x00000028   Code   RO         3565    i.__ARM_fpclassify  m_ws.l(fpclassify.o)
    0x08001dec   0x08001dec   0x00000006   Code   RO          329    i._sys_exit         usart.o
    0x08001df2   0x08001df2   0x00000002   PAD
    0x08001df4   0x08001df4   0x00000040   Code   RO          262    i.delay_init        delay.o
    0x08001e34   0x08001e34   0x0000003c   Code   RO          263    i.delay_ms          delay.o
    0x08001e70   0x08001e70   0x0000001c   Code   RO          330    i.fputc             usart.o
    0x08001e8c   0x08001e8c   0x000000b8   Code   RO            1    i.main              main.o
    0x08001f44   0x08001f44   0x00000084   Code   RO          331    i.uart_init         usart.o
    0x08001fc8   0x08001fc8   0x0000002c   Code   RO         3561    locale$$code        c_w.l(lc_numeric_c.o)
    0x08001ff4   0x08001ff4   0x00000062   Code   RO         3470    x$fpl$d2f           fz_ws.l(d2f.o)
    0x08002056   0x08002056   0x00000002   PAD
    0x08002058   0x08002058   0x000002b0   Code   RO         3473    x$fpl$ddiv          fz_ws.l(ddiv.o)
    0x08002308   0x08002308   0x00000026   Code   RO         3476    x$fpl$dfltu         fz_ws.l(dflt_clz.o)
    0x0800232e   0x0800232e   0x00000002   PAD
    0x08002330   0x08002330   0x00000154   Code   RO         3482    x$fpl$dmul          fz_ws.l(dmul.o)
    0x08002484   0x08002484   0x0000009c   Code   RO         3496    x$fpl$dnaninf       fz_ws.l(dnaninf.o)
    0x08002520   0x08002520   0x0000000c   Code   RO         3498    x$fpl$dretinf       fz_ws.l(dretinf.o)
    0x0800252c   0x0800252c   0x00000056   Code   RO         3484    x$fpl$f2d           fz_ws.l(f2d.o)
    0x08002582   0x08002582   0x0000008c   Code   RO         3500    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x0800260e   0x0800260e   0x0000000a   Code   RO         3502    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x08002618   0x08002618   0x00000004   Code   RO         3486    x$fpl$printf1       fz_ws.l(printf1.o)
    0x0800261c   0x0800261c   0x00000000   Code   RO         3504    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x0800261c   0x0800261c   0x00000094   Data   RO         3532    .constdata          c_w.l(bigflt0.o)
    0x080026b0   0x080026b0   0x00000020   Data   RO         3706    Region$$Table       anon$$obj.o
    0x080026d0   0x080026d0   0x0000001c   Data   RO         3560    locale$$data        c_w.l(lc_numeric_c.o)


    Execution Region ER_RW (Exec base: 0x20000000, Load base: 0x080026ec, Size: 0x00000024, Max: 0xffffffff, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x080026ec   0x00000008   Data   RW            2    .data               main.o
    0x20000008   0x080026f4   0x00000004   Data   RW          265    .data               delay.o
    0x2000000c   0x080026f8   0x00000004   Data   RW          333    .data               usart.o
    0x20000010   0x080026fc   0x00000014   Data   RW         1986    .data               stm32f10x_rcc.o


    Execution Region ER_ZI (Exec base: 0x20000024, Load base: 0x08002710, Size: 0x00000664, Max: 0xffffffff, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000024        -       0x00000060   Zero   RW         3568    .bss                c_w.l(libspace.o)
    0x20000084   0x08002710   0x00000004   PAD
    0x20000088        -       0x00000200   Zero   RW          383    HEAP                startup_stm32f10x_hd.o
    0x20000288        -       0x00000400   Zero   RW          382    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       246         16          0          0          0       2029   adc.o
       288         16          0          0          0       1481   atd5984.o
       124         12          0          4          0       1464   delay.o
        44          4          0          0          0        567   key.o
       184         44          0          8          0     263329   main.o
       132         22          0          0          0       1787   misc.o
        64         26        304          0       1536        860   startup_stm32f10x_hd.o
       394         10          0          0          0       8969   stm32f10x_adc.o
       286          0          0          0          0       3376   stm32f10x_gpio.o
        26          0          0          0          0       4470   stm32f10x_it.o
       300         38          0         20          0       5497   stm32f10x_rcc.o
       628         62          0          0          0       8260   stm32f10x_tim.o
       314          6          0          0          0       4026   stm32f10x_usart.o
         0          0          0          0          0         32   sys.o
       328         28          0          0          0       2145   system_stm32f10x.o
       112          0          0          0          0       1302   tim.o
       166         12          0          4          0       3842   usart.o

    ----------------------------------------------------------------------
      3656        <USER>        <GROUP>         36       1536     313436   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        20          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       104          0          0          0          0         84   __printf.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         8          0          0          0          0         68   ferror.o
         6          0          0          0          0        152   heapauxi.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        18          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
        24          4          0          0          0         84   noretval__2printf.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        98          4          0          0          0         92   d2f.o
       688        140          0          0          0        208   ddiv.o
        38          0          0          0          0         68   dflt_clz.o
       340         12          0          0          0        104   dmul.o
       156          4          0          0          0         92   dnaninf.o
        12          0          0          0          0         68   dretinf.o
        86          4          0          0          0         84   f2d.o
       140          4          0          0          0         84   fnaninf.o
        10          0          0          0          0         68   fretinf.o
         4          0          0          0          0         68   printf1.o
         0          0          0          0          0          0   usenofp.o
        40          0          0          0          0         68   fpclassify.o

    ----------------------------------------------------------------------
      5796        <USER>        <GROUP>          0        100       3360   Library Totals
        12          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      4172        188        176          0         96       2356   c_w.l
      1572        168          0          0          0        936   fz_ws.l
        40          0          0          0          0         68   m_ws.l

    ----------------------------------------------------------------------
      5796        <USER>        <GROUP>          0        100       3360   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      9452        652        512         36       1636     312112   Grand Totals
      9452        652        512         36       1636     312112   ELF Image Totals
      9452        652        512         36          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 9964 (   9.73kB)
    Total RW  Size (RW Data + ZI Data)              1672 (   1.63kB)
    Total ROM Size (Code + RO Data + RW Data)      10000 (   9.77kB)

==============================================================================

