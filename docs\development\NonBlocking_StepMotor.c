/**
 * 2025年电赛E题专用非阻塞步进电机控制系统
 *
 * 应用场景：摄像头识别目标 -> 串口发送角度指令 -> 电机精确转动
 *
 * 核心特性：
 * 1. 基于定时器中断的精确步数控制
 * 2. 非阻塞式API，响应摄像头指令
 * 3. 串口命令解析和执行
 * 4. 精确的角度控制和位置跟踪
 *
 * 使用流程：
 * 1. 调用 Camera_StepMotor_Init() 初始化
 * 2. 在主循环中调用 Camera_StepMotor_Process() 处理任务
 * 3. 摄像头通过串口发送角度指令，系统自动执行
 *
 * 版本：V4.0 电赛专用版
 */

#include "sys.h"
#include "stm32f10x.h"
#include "stm32f10x_gpio.h"
#include "stm32f10x_tim.h"
#include "stm32f10x_rcc.h"
#include "misc.h"
#include <stdio.h>
#include <math.h>
#include <string.h>

// ==================== 配置参数 ====================
#define STEP_ANGLE_PER_PULSE    0.1125f     // 每脉冲角度(16细分)
#define DEFAULT_PWM_FREQ        1428        // 默认PWM频率(Hz)
#define TIMER_INTERRUPT_FREQ    10000       // 定时器中断频率10kHz

// 电机方向和ID定义
#define MOTOR_DIR_FORWARD       0
#define MOTOR_DIR_REVERSE       1
#define MOTOR_1                 1
#define MOTOR_2                 2
#define MOTOR_BOTH              3

// ==================== 状态定义 ====================
typedef enum {
    MOTOR_STATE_IDLE = 0,        // 空闲状态
    MOTOR_STATE_RUNNING,         // 运行中
    MOTOR_STATE_COMPLETED        // 运行完成
} MotorState_t;

typedef struct {
    MotorState_t state;          // 当前状态
    uint32_t target_steps;       // 目标步数
    uint32_t current_steps;      // 当前步数
    uint16_t step_interval;      // 步进间隔（定时器计数）
    uint16_t step_counter;       // 步进计数器
    uint8_t motor_mask;          // 电机掩码
    float target_angle;          // 目标角度
    uint8_t direction;           // 运动方向
} MotorTask_t;

// ==================== 全局变量 ====================
static uint8_t motor_initialized = 0;
static MotorTask_t current_task = {0};
static float motor1_position = 0.0f;
static float motor2_position = 0.0f;

// ==================== 私有函数实现 ====================

/**
 * GPIO初始化
 */
static void init_gpio_pins(void) {
    GPIO_InitTypeDef GPIO_InitStructure;
    
    // 使能时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOC | RCC_APB2Periph_GPIOD, ENABLE);
    
    // 配置控制引脚
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_12 | GPIO_Pin_13 | GPIO_Pin_14;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOC, &GPIO_InitStructure);
    
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2;
    GPIO_Init(GPIOD, &GPIO_InitStructure);
    
    // 设置初始状态
    GPIO_SetBits(GPIOC, GPIO_Pin_12);    // 电机B唤醒
    GPIO_ResetBits(GPIOC, GPIO_Pin_13);  // 电机A正转
    GPIO_SetBits(GPIOC, GPIO_Pin_14);    // 电机B反转
    GPIO_SetBits(GPIOD, GPIO_Pin_2);     // 电机A唤醒
}

/**
 * TIM8 PWM初始化
 */
static void init_tim8_pwm(uint16_t freq_hz) {
    GPIO_InitTypeDef GPIO_InitStructure;
    TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;
    TIM_OCInitTypeDef TIM_OCInitStructure;
    
    // 使能时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_TIM8, ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOC, ENABLE);
    
    // 配置PWM输出引脚
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_8 | GPIO_Pin_9;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOC, &GPIO_InitStructure);
    
    // 计算定时器参数
    uint16_t prescaler = 6;
    uint16_t period = (72000000 / (freq_hz * (prescaler + 1))) - 1;
    uint16_t pulse = 50;
    
    // 配置定时器
    TIM_TimeBaseStructure.TIM_Period = period;
    TIM_TimeBaseStructure.TIM_Prescaler = prescaler;
    TIM_TimeBaseStructure.TIM_ClockDivision = 0;
    TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;
    TIM_TimeBaseInit(TIM8, &TIM_TimeBaseStructure);
    
    // 配置PWM输出
    TIM_OCInitStructure.TIM_OCMode = TIM_OCMode_PWM1;
    TIM_OCInitStructure.TIM_OutputState = TIM_OutputState_Enable;
    TIM_OCInitStructure.TIM_Pulse = pulse;
    TIM_OCInitStructure.TIM_OCPolarity = TIM_OCPolarity_High;
    
    TIM_OC3Init(TIM8, &TIM_OCInitStructure);
    TIM_OC4Init(TIM8, &TIM_OCInitStructure);
    
    TIM_OC3PreloadConfig(TIM8, TIM_OCPreload_Enable);
    TIM_OC4PreloadConfig(TIM8, TIM_OCPreload_Enable);
    
    // 使能主输出
    TIM_CtrlPWMOutputs(TIM8, ENABLE);
    TIM_ARRPreloadConfig(TIM8, ENABLE);
}

/**
 * TIM2控制定时器初始化（用于步数控制）
 */
static void init_control_timer(void) {
    TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;
    NVIC_InitTypeDef NVIC_InitStructure;
    
    // 使能TIM2时钟
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM2, ENABLE);
    
    // 配置定时器：10kHz中断频率
    TIM_TimeBaseStructure.TIM_Period = 719;      // 72MHz/10kHz/10 - 1
    TIM_TimeBaseStructure.TIM_Prescaler = 9;     // 预分频10
    TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;
    TIM_TimeBaseStructure.TIM_ClockDivision = 0;
    TIM_TimeBaseInit(TIM2, &TIM_TimeBaseStructure);
    
    // 使能中断
    TIM_ITConfig(TIM2, TIM_IT_Update, ENABLE);
    
    // 配置NVIC
    NVIC_InitStructure.NVIC_IRQChannel = TIM2_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
    
    // 启动定时器
    TIM_Cmd(TIM2, ENABLE);
}

/**
 * 计算步进间隔
 */
static uint16_t calculate_step_interval(uint16_t speed_rpm) {
    // 将RPM转换为步进间隔（10kHz定时器基准）
    // speed_rpm -> Hz -> 10kHz计数间隔
    uint16_t step_freq = (speed_rpm * 360 * 16) / (60 * 1.8);  // 步进频率
    if (step_freq == 0) step_freq = 1;
    uint16_t interval = TIMER_INTERRUPT_FREQ / step_freq;
    return (interval > 0) ? interval : 1;
}

// ==================== 中断服务函数 ====================

/**
 * TIM2中断服务函数 - 步数控制核心
 */
void TIM2_IRQHandler(void) {
    if (TIM_GetITStatus(TIM2, TIM_IT_Update) != RESET) {
        TIM_ClearITPendingBit(TIM2, TIM_IT_Update);
        
        // 处理当前任务
        if (current_task.state == MOTOR_STATE_RUNNING) {
            current_task.step_counter++;
            
            // 检查是否到达步进间隔
            if (current_task.step_counter >= current_task.step_interval) {
                current_task.step_counter = 0;
                current_task.current_steps++;
                
                // 检查是否完成
                if (current_task.current_steps >= current_task.target_steps) {
                    // 停止PWM
                    TIM_Cmd(TIM8, DISABLE);
                    current_task.state = MOTOR_STATE_COMPLETED;
                }
            }
        }
    }
}

// ==================== 公共API实现 ====================

/**
 * 系统初始化
 */
void NonBlocking_StepMotor_Init(void) {
    if (motor_initialized) {
        printf("警告：系统已初始化\r\n");
        return;
    }
    
    // 初始化各个模块
    init_gpio_pins();
    init_tim8_pwm(DEFAULT_PWM_FREQ);
    init_control_timer();
    
    // 初始化状态
    current_task.state = MOTOR_STATE_IDLE;
    motor1_position = 0.0f;
    motor2_position = 0.0f;
    
    motor_initialized = 1;
    printf("非阻塞步进电机系统初始化完成\r\n");
}

/**
 * 检查电机是否空闲
 */
uint8_t StepMotor_IsIdle(void) {
    return (current_task.state == MOTOR_STATE_IDLE || 
            current_task.state == MOTOR_STATE_COMPLETED);
}

/**
 * 等待当前任务完成
 */
void StepMotor_WaitComplete(void) {
    while (!StepMotor_IsIdle()) {
        // 可以在这里添加其他处理
        // 或者调用系统任务调度
    }
}

/**
 * 非阻塞角度控制（双电机同步）
 */
uint8_t StepMotor_RotateAngle_NB(float angle_degrees, uint16_t speed_rpm) {
    if (!motor_initialized) return 1;
    if (!StepMotor_IsIdle()) return 2;  // 系统忙
    
    // 计算参数
    uint32_t steps = (uint32_t)(fabs(angle_degrees) / STEP_ANGLE_PER_PULSE);
    uint16_t interval = calculate_step_interval(speed_rpm);
    uint8_t direction = (angle_degrees < 0) ? MOTOR_DIR_REVERSE : MOTOR_DIR_FORWARD;
    
    printf("开始非阻塞转动%.1f度，%d步，间隔%d\r\n", angle_degrees, steps, interval);
    
    // 设置方向
    GPIO_WriteBit(GPIOC, GPIO_Pin_13, direction ? Bit_SET : Bit_RESET);
    GPIO_WriteBit(GPIOC, GPIO_Pin_14, direction ? Bit_SET : Bit_RESET);
    
    // 配置任务
    current_task.state = MOTOR_STATE_RUNNING;
    current_task.target_steps = steps;
    current_task.current_steps = 0;
    current_task.step_interval = interval;
    current_task.step_counter = 0;
    current_task.motor_mask = MOTOR_BOTH;
    current_task.target_angle = angle_degrees;
    current_task.direction = direction;
    
    // 启动PWM
    TIM_Cmd(TIM8, ENABLE);
    
    return 0;  // 成功启动
}

/**
 * 处理任务完成
 */
void StepMotor_Process(void) {
    if (current_task.state == MOTOR_STATE_COMPLETED) {
        // 更新位置
        if (current_task.motor_mask & 0x01) {  // 电机1
            motor1_position += current_task.target_angle;
        }
        if (current_task.motor_mask & 0x02) {  // 电机2
            motor2_position += current_task.target_angle;
        }
        
        printf("转动完成！位置：M1=%.1f°, M2=%.1f°\r\n", motor1_position, motor2_position);
        
        // 重置任务状态
        current_task.state = MOTOR_STATE_IDLE;
    }
}

/**
 * 获取当前位置
 */
void StepMotor_GetPosition(float* motor1_pos, float* motor2_pos) {
    *motor1_pos = motor1_position;
    *motor2_pos = motor2_position;
}

/**
 * 紧急停止
 */
void StepMotor_EmergencyStop(void) {
    TIM_Cmd(TIM8, DISABLE);
    current_task.state = MOTOR_STATE_IDLE;
    printf("紧急停止！\r\n");
}
