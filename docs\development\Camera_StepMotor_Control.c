/**
 * 2025年电赛E题专用步进电机控制系统
 * 
 * 应用场景：摄像头识别 -> 串口角度指令 -> 电机精确转动
 * 
 * 核心功能：
 * 1. 非阻塞电机控制，不影响主程序运行
 * 2. 串口命令解析，接收摄像头角度指令
 * 3. 精确角度控制，支持双电机独立/同步控制
 * 4. 实时位置跟踪和状态反馈
 * 
 * 串口指令格式：
 * - "M1:90.5"     -> 电机1转动90.5度
 * - "M2:-45.0"    -> 电机2反转45度  
 * - "MB:180.0"    -> 双电机同步转动180度
 * - "POS"         -> 查询当前位置
 * - "STOP"        -> 紧急停止
 * - "RESET"       -> 位置归零
 * 
 * 版本：V1.0 电赛专用版
 */

#include "sys.h"
#include "delay.h"
#include "usart.h"
#include <stdio.h>
#include <string.h>
#include <math.h>

// ==================== 配置参数 ====================
#define STEP_ANGLE_PER_PULSE    0.1125f     // 每脉冲角度(16细分)
#define DEFAULT_SPEED_RPM       30          // 默认转速
#define MAX_COMMAND_LENGTH      32          // 最大命令长度

// 电机状态定义
typedef enum {
    MOTOR_IDLE = 0,          // 空闲状态
    MOTOR_RUNNING,           // 运行中
    MOTOR_COMPLETED          // 运行完成
} MotorState_t;

// 电机任务结构
typedef struct {
    MotorState_t state;      // 当前状态
    uint8_t motor_id;        // 电机ID (1=M1, 2=M2, 3=双电机)
    float target_angle;      // 目标角度
    uint16_t speed_rpm;      // 转速
    uint32_t start_time;     // 开始时间
    uint32_t duration_ms;    // 运行时长
} MotorTask_t;

// ==================== 全局变量 ====================
static uint8_t system_initialized = 0;
static MotorTask_t current_task = {0};
static float motor1_position = 0.0f;    // 电机1当前位置
static float motor2_position = 0.0f;    // 电机2当前位置
static char uart_command[MAX_COMMAND_LENGTH];
static uint8_t command_ready = 0;

// ==================== 外部函数声明 ====================
// 这些函数来自原有的ATD5984.c，需要保持兼容
extern void ATD5984_Init(void);
extern void change_motor_speed(uint16_t freq);
extern void stop_motors(void);
extern void start_motors(void);
extern void set_motors_direction(uint8_t motor1_dir, uint8_t motor2_dir);

// ==================== 私有函数实现 ====================

/**
 * 计算运行时间
 */
static uint32_t calculate_run_time(float angle, uint16_t speed_rpm) {
    // 运行时间 = 角度 / (转速 * 360度/分钟) * 60000毫秒/分钟
    float time_minutes = fabs(angle) / (speed_rpm * 360.0f);
    return (uint32_t)(time_minutes * 60000.0f);
}

/**
 * 计算PWM频率
 */
static uint16_t calculate_pwm_freq(uint16_t speed_rpm) {
    // PWM频率 = (转速 * 360 * 16细分) / (60秒 * 1.8度步距角)
    return (speed_rpm * 360 * 16) / (60 * 1.8);
}

/**
 * 设置电机方向
 */
static void set_motor_direction(uint8_t motor_id, float angle) {
    uint8_t dir1 = 0, dir2 = 0;
    
    if (angle < 0) {
        dir1 = dir2 = 1;  // 反转
    }
    
    if (motor_id == 1) {
        // 只设置电机1方向
        GPIO_WriteBit(GPIOC, GPIO_Pin_13, dir1 ? Bit_SET : Bit_RESET);
    } else if (motor_id == 2) {
        // 只设置电机2方向
        GPIO_WriteBit(GPIOC, GPIO_Pin_14, dir2 ? Bit_SET : Bit_RESET);
    } else {
        // 双电机同向
        set_motors_direction(dir1, dir2);
    }
}

/**
 * 启动电机任务
 */
static uint8_t start_motor_task(uint8_t motor_id, float angle, uint16_t speed_rpm) {
    if (current_task.state == MOTOR_RUNNING) {
        return 1;  // 系统忙
    }
    
    // 计算参数
    uint32_t duration = calculate_run_time(angle, speed_rpm);
    uint16_t pwm_freq = calculate_pwm_freq(speed_rpm);
    
    printf("启动电机%d，角度%.1f°，速度%dRPM，用时%ldms\r\n", 
           motor_id, angle, speed_rpm, duration);
    
    // 设置方向
    set_motor_direction(motor_id, angle);
    
    // 电机使能控制
    if (motor_id == 1) {
        GPIO_ResetBits(GPIOC, GPIO_Pin_12);  // 电机2休眠
        GPIO_SetBits(GPIOD, GPIO_Pin_2);     // 电机1唤醒
    } else if (motor_id == 2) {
        GPIO_ResetBits(GPIOD, GPIO_Pin_2);   // 电机1休眠
        GPIO_SetBits(GPIOC, GPIO_Pin_12);    // 电机2唤醒
    } else {
        GPIO_SetBits(GPIOC, GPIO_Pin_12);    // 电机2唤醒
        GPIO_SetBits(GPIOD, GPIO_Pin_2);     // 电机1唤醒
    }
    
    // 配置任务
    current_task.state = MOTOR_RUNNING;
    current_task.motor_id = motor_id;
    current_task.target_angle = angle;
    current_task.speed_rpm = speed_rpm;
    current_task.start_time = get_system_time_ms();
    current_task.duration_ms = duration;
    
    // 启动电机
    change_motor_speed(pwm_freq);
    
    return 0;  // 成功
}

/**
 * 解析串口命令
 */
static void parse_uart_command(char* cmd) {
    printf("收到命令: %s\r\n", cmd);
    
    if (strncmp(cmd, "M1:", 3) == 0) {
        // 电机1控制
        float angle = atof(cmd + 3);
        if (start_motor_task(1, angle, DEFAULT_SPEED_RPM) == 0) {
            printf("电机1开始转动%.1f度\r\n", angle);
        } else {
            printf("电机忙，指令被忽略\r\n");
        }
        
    } else if (strncmp(cmd, "M2:", 3) == 0) {
        // 电机2控制
        float angle = atof(cmd + 3);
        if (start_motor_task(2, angle, DEFAULT_SPEED_RPM) == 0) {
            printf("电机2开始转动%.1f度\r\n", angle);
        } else {
            printf("电机忙，指令被忽略\r\n");
        }
        
    } else if (strncmp(cmd, "MB:", 3) == 0) {
        // 双电机同步控制
        float angle = atof(cmd + 3);
        if (start_motor_task(3, angle, DEFAULT_SPEED_RPM) == 0) {
            printf("双电机开始同步转动%.1f度\r\n", angle);
        } else {
            printf("电机忙，指令被忽略\r\n");
        }
        
    } else if (strcmp(cmd, "POS") == 0) {
        // 查询位置
        printf("当前位置: M1=%.1f°, M2=%.1f°\r\n", motor1_position, motor2_position);
        
    } else if (strcmp(cmd, "STOP") == 0) {
        // 紧急停止
        stop_motors();
        current_task.state = MOTOR_IDLE;
        printf("紧急停止执行\r\n");
        
    } else if (strcmp(cmd, "RESET") == 0) {
        // 位置归零
        motor1_position = 0.0f;
        motor2_position = 0.0f;
        printf("位置已归零\r\n");
        
    } else {
        printf("未知命令: %s\r\n", cmd);
        printf("支持的命令: M1:angle, M2:angle, MB:angle, POS, STOP, RESET\r\n");
    }
}

// ==================== 公共API实现 ====================

/**
 * 系统初始化
 */
void Camera_StepMotor_Init(void) {
    if (system_initialized) {
        printf("警告：系统已初始化\r\n");
        return;
    }
    
    // 初始化硬件
    ATD5984_Init();
    
    // 初始化状态
    current_task.state = MOTOR_IDLE;
    motor1_position = 0.0f;
    motor2_position = 0.0f;
    command_ready = 0;
    
    system_initialized = 1;
    printf("摄像头步进电机控制系统初始化完成\r\n");
    printf("支持命令: M1:angle, M2:angle, MB:angle, POS, STOP, RESET\r\n");
}

/**
 * 主处理函数
 */
void Camera_StepMotor_Process(void) {
    // 处理串口命令
    if (command_ready) {
        parse_uart_command(uart_command);
        command_ready = 0;
    }
    
    // 处理电机任务
    if (current_task.state == MOTOR_RUNNING) {
        uint32_t current_time = get_system_time_ms();
        
        // 检查是否完成
        if (current_time - current_task.start_time >= current_task.duration_ms) {
            // 停止电机
            stop_motors();
            
            // 更新位置
            if (current_task.motor_id == 1) {
                motor1_position += current_task.target_angle;
            } else if (current_task.motor_id == 2) {
                motor2_position += current_task.target_angle;
            } else {
                motor1_position += current_task.target_angle;
                motor2_position += current_task.target_angle;
            }
            
            // 重新启用所有电机
            GPIO_SetBits(GPIOC, GPIO_Pin_12);    // 电机2唤醒
            GPIO_SetBits(GPIOD, GPIO_Pin_2);     // 电机1唤醒
            
            printf("转动完成！当前位置: M1=%.1f°, M2=%.1f°\r\n", 
                   motor1_position, motor2_position);
            
            current_task.state = MOTOR_COMPLETED;
        }
    } else if (current_task.state == MOTOR_COMPLETED) {
        current_task.state = MOTOR_IDLE;
    }
}

/**
 * 串口数据接收处理
 */
void Camera_StepMotor_UART_Handler(uint8_t data) {
    static uint8_t buffer_index = 0;
    static char buffer[MAX_COMMAND_LENGTH];
    
    if (data == '\n' || data == '\r') {
        if (buffer_index > 0) {
            buffer[buffer_index] = '\0';
            strcpy(uart_command, buffer);
            command_ready = 1;
            buffer_index = 0;
        }
    } else if (buffer_index < MAX_COMMAND_LENGTH - 1) {
        buffer[buffer_index++] = data;
    }
}

/**
 * 检查系统是否空闲
 */
uint8_t Camera_StepMotor_IsIdle(void) {
    return (current_task.state == MOTOR_IDLE);
}

/**
 * 获取当前位置
 */
void Camera_StepMotor_GetPosition(float* motor1_pos, float* motor2_pos) {
    *motor1_pos = motor1_position;
    *motor2_pos = motor2_position;
}

/**
 * 直接角度控制（用于程序内部调用）
 */
uint8_t Camera_StepMotor_RotateAngle(uint8_t motor_id, float angle, uint16_t speed_rpm) {
    return start_motor_task(motor_id, angle, speed_rpm);
}

// ==================== 系统时间实现 ====================

static volatile uint32_t system_time_ms = 0;

/**
 * 更新系统时间（在主循环中调用）
 * 这是最安全的时间实现方案，避免SysTick冲突
 */
void update_system_time(uint32_t interval_ms) {
    system_time_ms += interval_ms;
}

/**
 * 系统时间获取
 * 基于主循环的简单时间管理，适合电赛应用
 */
uint32_t get_system_time_ms(void) {
    return system_time_ms;
}

/**
 * 重置系统时间
 */
void reset_system_time(void) {
    system_time_ms = 0;
}
